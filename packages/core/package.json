{"name": "@gauzy/core", "version": "0.1.0", "description": "Ever Gauzy Platform Core - a headless ERP/CRM/HRM framework", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/core"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build core", "lib:build:prod": "yarn nx build core", "lib:watch": "yarn nx build core --watch", "start:api": "nodemon", "start:api:debug": "nodemon --config nodemon-debug.json", "typeorm": "ts-node -r tsconfig-paths/register ./../../node_modules/typeorm/cli.js", "migration:run": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register ./src/lib/database/migration.ts migration:run", "migration:generate": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register ./src/lib/database/migration.ts migration:generate", "migration:revert": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register ./src/lib/database/migration.ts migration:revert", "migration:create": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register ./src/lib/database/migration.ts migration:create"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/server": "^4.12.0", "@apollo/subgraph": "^2.10.2", "@aws-sdk/client-s3": "3.717.0", "@aws-sdk/s3-request-presigner": "3.717.0", "@faker-js/faker": "^9.8.0", "@fastify/swagger": "^9.5.0", "@gauzy/auth": "^0.1.0", "@gauzy/common": "^0.1.0", "@gauzy/config": "^0.1.0", "@gauzy/constants": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@gauzy/plugin": "^0.1.0", "@gauzy/utils": "^0.1.0", "@godaddy/terminus": "^4.12.1", "@grpc/grpc-js": "^1.7.3", "@honeycombio/opentelemetry-node": "0.6.1", "@mikro-orm/better-sqlite": "^6.4.13", "@mikro-orm/core": "^6.4.13", "@mikro-orm/knex": "^6.4.13", "@mikro-orm/mongodb": "^6.4.13", "@mikro-orm/mysql": "^6.4.13", "@mikro-orm/nestjs": "^6.1.1", "@mikro-orm/postgresql": "^6.4.13", "@nestjs/apollo": "^13.1.0", "@nestjs/axios": "github:ever-co/nestjs-axios#master", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/cqrs": "^11.0.3", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^11.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.1.5", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.0", "@nestjsx/crud": "^5.0.0-alpha.3", "@nestjsx/crud-typeorm": "^5.0.0-alpha.3", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.57.2", "@opentelemetry/auto-instrumentations-node": "^0.56.1", "@opentelemetry/context-async-hooks": "^1.30.1", "@opentelemetry/core": "^1.30.1", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.57.2", "@opentelemetry/exporter-metrics-otlp-proto": "^0.57.2", "@opentelemetry/exporter-trace-otlp-grpc": "^0.57.2", "@opentelemetry/exporter-trace-otlp-http": "^0.57.2", "@opentelemetry/exporter-trace-otlp-proto": "^0.57.2", "@opentelemetry/exporter-zipkin": "^1.30.1", "@opentelemetry/instrumentation": "^0.57.2", "@opentelemetry/propagator-b3": "^1.30.1", "@opentelemetry/propagator-jaeger": "^1.30.1", "@opentelemetry/resource-detector-container": "^0.6.1", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-logs": "^0.57.2", "@opentelemetry/sdk-metrics": "^1.30.1", "@opentelemetry/sdk-node": "^0.57.2", "@opentelemetry/sdk-trace-base": "^1.30.1", "@opentelemetry/sdk-trace-node": "^1.30.1", "@opentelemetry/semantic-conventions": "^1.30.0", "@sentry/electron": "6.9.0", "@sentry/node": "9.43.0", "@sentry/profiling-node": "9.43.0", "@sentry/core": "9.43.0", "app-root-path": "^3.0.0", "archiver": "^5.3.0", "atlassian-connect-express": "^8.5.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "better-sqlite3": "9.6.0", "cache-manager": "^6.4.2", "cache-manager-redis-yet": "^4.1.2", "camelcase": "^6.3.0", "chalk": "^4.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudinary": "^1.33.0", "connect-redis": "^7.1.0", "cross-fetch": "^3.1.5", "csurf": "^1.11.0", "csv-parser": "^2.3.2", "csv-writer": "^1.6.0", "currency.js": "^2.0.3", "date-fns": "^2.28.0", "dotenv": "^16.0.3", "email-templates": "^8.0.8", "express": "^5.1.0", "express-session": "^1.18.1", "fast-json-stringify": "^2.4.1", "fs-extra": "^10.1.0", "graphql": "^16.11.0", "graphql-playground-middleware-express": "^1.7.23", "graphql-subscriptions": "^3.0.0", "graphql-tools": "^9.0.10", "handlebars": "^4.7.6", "helmet": "^4.1.1", "jimp": "^0.22.7", "jsonwebtoken": "^9.0.0", "kafkajs": "^1.14.0", "knex": "^3.1.0", "libsql": "^0.3.16", "mikro-orm-soft-delete": "^1.0.0-alpha.1", "mjml": "^4.14.1", "mkdirp": "^3.0.1", "moment": "^2.30.1", "moment-range": "^4.0.2", "moment-timezone": "^0.5.48", "mqtt": "^4.3.7", "multer": "^2.0.0", "multer-s3": "^3.0.1", "multer-storage-cloudinary": "^4.0.0", "mysql2": "^3.12.0", "nats": "^2.6.1", "nest-knexjs": "^0.0.26", "nestjs-cls": "^5.4.3", "nestjs-i18n": "^10.5.1", "node-fetch": "^2.6.7", "nodemailer": "^6.4.11", "nodemailer-handlebars": "^1.0.1", "opentelemetry-instrumentation-typeorm": "^0.40.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdfmake": "^0.2.0", "pg": "^8.13.1", "pg-query-stream": "^4.7.1", "prettier": "^2.8.4", "redis": "^4.6.12", "reflect-metadata": "^0.2.2", "request": "^2.88.2", "rxjs": "^7.8.2", "sharp": "^0.33.5", "sql.js": "^1.5.0", "streamifier": "^0.1.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.24", "underscore": "^1.13.3", "unleash-client": "^3.16.1", "unzipper": "^0.10.11", "uuid": "^11.1.0", "web-push": "^3.4.4", "yargs": "^17.5.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/email-templates": "^7.1.0", "@types/express": "^5.0.1", "@types/fs-extra": "5.0.2", "@types/graphql": "^14.5.0", "@types/html-to-text": "^9.0.1", "@types/i18n": "^0.12.0", "@types/jest": "29.5.14", "@types/multer": "^1.4.12", "@types/node": "^20.14.9", "@types/node-fetch": "^2.5.12", "@types/nodemailer": "^6.4.0", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/socket.io": "^2.1.12", "@types/supertest": "^2.0.10", "@types/web-push": "^3.3.0", "@types/yargs": "^15.0.9", "copyfiles": "^2.4.1", "jest": "^29.7.0", "nodemon": "^3.1.0", "rimraf": "^3.0.2", "supertest": "^6.0.1", "ts-jest": "29.1.1", "ts-node": "^10.9.2", "tslint-config-prettier": "^1.18.0", "typescript": "^5.8.3"}, "keywords": ["gauzy", "core", "erp", "crm", "hrm", "api", "framework", "<PERSON><PERSON><PERSON>", "microservices", "graphql", "typeorm", "open-source", "enterprise", "business", "headless", "platform", "software", "saas", "authentication", "authorization", "api-core", "backend", "server", "i18n", "integration"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}